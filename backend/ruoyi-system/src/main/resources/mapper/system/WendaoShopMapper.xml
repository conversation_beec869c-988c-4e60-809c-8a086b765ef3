<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.WendaoShopMapper">
    
    <resultMap type="WendaoShop" id="WendaoShopResult">
        <id     property="id"                   column="id"                   />
        <result property="creatorId"            column="creator_id"           />
        <result property="createdAt"            column="created_at"           />
        <result property="expireTime"           column="expire_time"          />
        <result property="isSealed"             column="is_sealed"            />
        <result property="shopId"               column="shop_id"              />
        <result property="shopLogo"             column="shop_logo"            />
        <result property="shopName"             column="shop_name"            />
        <result property="useCollection"        column="use_collection"       />
        <result property="versionType"          column="version_type"         />
        <result property="rightsType"           column="rights_type"          />
        <result property="appId"                column="app_id"               />
        <result property="hasExpired"           column="has_expired"          />
        <result property="hasActivateOrder"     column="has_activate_order"   />
        <result property="status"               column="status"               />
        <result property="lastLogin"            column="last_login"           />
        <result property="isWaitSeal"           column="is_wait_seal"         />
        <result property="sealApplyTime"        column="seal_apply_time"      />
        <result property="readySealAt"          column="ready_seal_at"        />
        <result property="isDrop"               column="is_drop"              />
        <result property="entryMode"            column="entry_mode"           />
        <result property="isTry"                column="is_try"               />
        <result property="isShowVersion"        column="is_show_version"      />
        <result property="showRenewal"          column="show_renewal"         />
        <result property="showExpireTime"       column="show_expire_time"     />
        <result property="createTime"           column="create_time"          />
        <result property="updateTime"           column="update_time"          />
    </resultMap>
    
    <sql id="selectWendaoShopVo">
        select id, creator_id, created_at, expire_time, is_sealed, shop_id, shop_logo, shop_name,
               use_collection, version_type, rights_type, app_id, has_expired, has_activate_order,
               status, last_login, is_wait_seal, seal_apply_time, ready_seal_at, is_drop,
               entry_mode, is_try, is_show_version, show_renewal, show_expire_time,
               create_time, update_time
        from wendao_shop
    </sql>
    
    <select id="selectWendaoShopList" parameterType="WendaoShop" resultMap="WendaoShopResult">
        <include refid="selectWendaoShopVo"/>
        <where>
            <if test="creatorId != null and creatorId != ''">
                AND creator_id = #{creatorId}
            </if>
            <if test="shopId != null and shopId != ''">
                AND shop_id = #{shopId}
            </if>
            <if test="shopName != null and shopName != ''">
                AND shop_name like concat('%', #{shopName}, '%')
            </if>
            <if test="appId != null and appId != ''">
                AND app_id = #{appId}
            </if>
            <if test="isSealed != null">
                AND is_sealed = #{isSealed}
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
            <if test="hasExpired != null">
                AND has_expired = #{hasExpired}
            </if>
            <if test="isDrop != null">
                AND is_drop = #{isDrop}
            </if>
            <if test="versionType != null">
                AND version_type = #{versionType}
            </if>
            <if test="isTry != null">
                AND is_try = #{isTry}
            </if>
            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                AND date_format(created_at,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
            </if>
            <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                AND date_format(created_at,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
            </if>
        </where>
        order by created_at desc
    </select>
    
    <select id="selectWendaoShopById" parameterType="Long" resultMap="WendaoShopResult">
        <include refid="selectWendaoShopVo"/>
        where id = #{id}
    </select>
    
    <select id="selectWendaoShopByShopId" parameterType="String" resultMap="WendaoShopResult">
        <include refid="selectWendaoShopVo"/>
        where shop_id = #{shopId}
    </select>
    
    <select id="selectWendaoShopByAppId" parameterType="String" resultMap="WendaoShopResult">
        <include refid="selectWendaoShopVo"/>
        where app_id = #{appId}
    </select>
    
    <select id="selectWendaoShopByCreatorId" parameterType="String" resultMap="WendaoShopResult">
        <include refid="selectWendaoShopVo"/>
        where creator_id = #{creatorId} and is_drop = 0 and last_login = 1 limit 1
    </select>
    
    <select id="selectWendaoShopListByCreatorId" parameterType="String" resultMap="WendaoShopResult">
        <include refid="selectWendaoShopVo"/>
        where creator_id = #{creatorId} and is_drop = 0
        order by created_at desc
    </select>
    
    <select id="checkShopIdUnique" parameterType="String" resultMap="WendaoShopResult">
        <include refid="selectWendaoShopVo"/>
        where shop_id = #{shopId} limit 1
    </select>
    
    <select id="checkAppIdUnique" parameterType="String" resultMap="WendaoShopResult">
        <include refid="selectWendaoShopVo"/>
        where app_id = #{appId} limit 1
    </select>
    
    <select id="selectExpiringSoonShops" resultMap="WendaoShopResult">
        <include refid="selectWendaoShopVo"/>
        where is_drop = 0 and has_expired = 0 and expire_time between curdate() and date_add(curdate(), interval #{days} day)
        order by expire_time asc
    </select>
    
    <select id="selectExpiredShops" resultMap="WendaoShopResult">
        <include refid="selectWendaoShopVo"/>
        where is_drop = 0 and expire_time &lt; curdate()
        order by expire_time desc
    </select>
    <select id="selectWendaoShopByShopIdAndCreatorId" resultMap="WendaoShopResult">
        <include refid="selectWendaoShopVo"/>
        where is_drop = 0 and creator_id = #{creatorId} and shop_id = #{shopId} limit 1
    </select>

    <insert id="insertWendaoShop" parameterType="WendaoShop" useGeneratedKeys="true" keyProperty="id">
        insert into wendao_shop(
            <if test="creatorId != null and creatorId != ''">creator_id,</if>
            <if test="createdAt != null">created_at,</if>
            <if test="expireTime != null">expire_time,</if>
            <if test="isSealed != null">is_sealed,</if>
            <if test="shopId != null and shopId != ''">shop_id,</if>
            <if test="shopLogo != null and shopLogo != ''">shop_logo,</if>
            <if test="shopName != null and shopName != ''">shop_name,</if>
            <if test="useCollection != null">use_collection,</if>
            <if test="versionType != null">version_type,</if>
            <if test="rightsType != null">rights_type,</if>
            <if test="appId != null and appId != ''">app_id,</if>
            <if test="hasExpired != null">has_expired,</if>
            <if test="hasActivateOrder != null">has_activate_order,</if>
            <if test="status != null">status,</if>
            <if test="lastLogin != null">last_login,</if>
            <if test="isWaitSeal != null">is_wait_seal,</if>
            <if test="sealApplyTime != null">seal_apply_time,</if>
            <if test="readySealAt != null">ready_seal_at,</if>
            <if test="isDrop != null">is_drop,</if>
            <if test="entryMode != null">entry_mode,</if>
            <if test="isTry != null">is_try,</if>
            <if test="isShowVersion != null">is_show_version,</if>
            <if test="showRenewal != null">show_renewal,</if>
            <if test="showExpireTime != null">show_expire_time,</if>
            create_time
        )values(
            <if test="creatorId != null and creatorId != ''">#{creatorId},</if>
            <if test="createdAt != null">#{createdAt},</if>
            <if test="expireTime != null">#{expireTime},</if>
            <if test="isSealed != null">#{isSealed},</if>
            <if test="shopId != null and shopId != ''">#{shopId},</if>
            <if test="shopLogo != null and shopLogo != ''">#{shopLogo},</if>
            <if test="shopName != null and shopName != ''">#{shopName},</if>
            <if test="useCollection != null">#{useCollection},</if>
            <if test="versionType != null">#{versionType},</if>
            <if test="rightsType != null">#{rightsType},</if>
            <if test="appId != null and appId != ''">#{appId},</if>
            <if test="hasExpired != null">#{hasExpired},</if>
            <if test="hasActivateOrder != null">#{hasActivateOrder},</if>
            <if test="status != null">#{status},</if>
            <if test="lastLogin != null">#{lastLogin},</if>
            <if test="isWaitSeal != null">#{isWaitSeal},</if>
            <if test="sealApplyTime != null">#{sealApplyTime},</if>
            <if test="readySealAt != null">#{readySealAt},</if>
            <if test="isDrop != null">#{isDrop},</if>
            <if test="entryMode != null">#{entryMode},</if>
            <if test="isTry != null">#{isTry},</if>
            <if test="isShowVersion != null">#{isShowVersion},</if>
            <if test="showRenewal != null">#{showRenewal},</if>
            <if test="showExpireTime != null">#{showExpireTime},</if>
            current_timestamp
        )
    </insert>

    <update id="updateWendaoShop" parameterType="WendaoShop">
        update wendao_shop
        <set>
            <if test="creatorId != null and creatorId != ''">creator_id = #{creatorId},</if>
            <if test="createdAt != null">created_at = #{createdAt},</if>
            <if test="expireTime != null">expire_time = #{expireTime},</if>
            <if test="isSealed != null">is_sealed = #{isSealed},</if>
            <if test="shopId != null and shopId != ''">shop_id = #{shopId},</if>
            <if test="shopLogo != null">shop_logo = #{shopLogo},</if>
            <if test="shopName != null and shopName != ''">shop_name = #{shopName},</if>
            <if test="useCollection != null">use_collection = #{useCollection},</if>
            <if test="versionType != null">version_type = #{versionType},</if>
            <if test="rightsType != null">rights_type = #{rightsType},</if>
            <if test="appId != null and appId != ''">app_id = #{appId},</if>
            <if test="hasExpired != null">has_expired = #{hasExpired},</if>
            <if test="hasActivateOrder != null">has_activate_order = #{hasActivateOrder},</if>
            <if test="status != null">status = #{status},</if>
            <if test="lastLogin != null">last_login = #{lastLogin},</if>
            <if test="isWaitSeal != null">is_wait_seal = #{isWaitSeal},</if>
            <if test="sealApplyTime != null">seal_apply_time = #{sealApplyTime},</if>
            <if test="readySealAt != null">ready_seal_at = #{readySealAt},</if>
            <if test="isDrop != null">is_drop = #{isDrop},</if>
            <if test="entryMode != null">entry_mode = #{entryMode},</if>
            <if test="isTry != null">is_try = #{isTry},</if>
            <if test="isShowVersion != null">is_show_version = #{isShowVersion},</if>
            <if test="showRenewal != null">show_renewal = #{showRenewal},</if>
            <if test="showExpireTime != null">show_expire_time = #{showExpireTime},</if>
            update_time = current_timestamp
        </set>
        where id = #{id}
    </update>

    <delete id="deleteWendaoShopById" parameterType="Long">
        delete from wendao_shop where id = #{id}
    </delete>

    <delete id="deleteWendaoShopByIds" parameterType="String">
        delete from wendao_shop where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    
    <update id="updateExpiredStatus">
        update wendao_shop set has_expired = 1, update_time = current_timestamp
        where expire_time &lt; curdate() and has_expired = 0 and is_drop = 0
    </update>

    <update id="clearLastLoginByCreatorId" parameterType="String">
        update wendao_shop set last_login = 0, update_time = current_timestamp
        where creator_id = #{creatorId}
    </update>

    <update id="setLastLoginShop" parameterType="String">
        update wendao_shop set last_login = 1, update_time = current_timestamp
        where shop_id = #{shopId}
    </update>
</mapper>
